<!-- 文案详情模态框内容 -->
<style>
.detail-modal .content-area {
    background: white;
    border-radius: 12px;
    padding: 25px;
    border: 1px solid #e9ecef;
}

.detail-modal .content-title {
    color: #495057;
    font-weight: 600;
    font-size: 22px;
    margin-bottom: 20px;
    display: flex;
    align-items: center;
    gap: 10px;
}

.detail-modal .content-text {
    background: #f8f9fa;
    padding: 20px;
    border-radius: 8px;
    border-left: 4px solid #007bff;
    line-height: 1.8;
    font-size: 15px;
    color: #495057;
    white-space: pre-line;
    word-wrap: break-word;
    text-align: left;
    margin-bottom: 25px;
}

.detail-modal .bottom-info-section {
    border-top: 1px solid #e9ecef;
    padding-top: 20px;
}

.detail-modal .info-group {
    margin-bottom: 20px;
    padding-bottom: 15px;
    border-bottom: 1px solid #f1f3f4;
}

.detail-modal .info-group:last-child {
    border-bottom: none;
    margin-bottom: 0;
    padding-bottom: 0;
}

.detail-modal .info-title {
    font-size: 16px;
    font-weight: 600;
    color: #495057;
    margin-bottom: 12px;
    display: flex;
    align-items: center;
    gap: 8px;
}

.detail-modal .tags-container {
    display: flex;
    flex-wrap: wrap;
    gap: 6px;
}

.detail-modal .badge {
    font-size: 12px;
    padding: 6px 12px;
    border-radius: 20px;
}

.detail-modal .location-info {
    color: #dc3545;
    font-size: 16px;
    font-weight: 500;
    display: flex;
    align-items: center;
    gap: 8px;
}

.detail-modal .user-info {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.detail-modal .right-panel {
    background: #f8f9fa;
    border-radius: 12px;
    padding: 20px;
    border: 1px solid #e9ecef;
    height: fit-content;
    position: sticky;
    top: 20px;
}

.detail-modal .section-title {
    font-size: 16px;
    font-weight: 600;
    color: #495057;
    margin-bottom: 15px;
    display: flex;
    align-items: center;
    gap: 8px;
}

.detail-modal .images-section {
    margin-bottom: 25px;
    padding-bottom: 20px;
    border-bottom: 1px solid #dee2e6;
}

.detail-modal .image-grid-right {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(80px, 1fr));
    gap: 10px;
    margin-top: 10px;
}

.detail-modal .image-item-right {
    position: relative;
    border-radius: 8px;
    overflow: hidden;
    cursor: pointer;
    transition: transform 0.2s ease, box-shadow 0.2s ease;
    border: 2px solid #e9ecef;
}

.detail-modal .image-item-right:hover {
    transform: scale(1.05);
    box-shadow: 0 6px 20px rgba(0,0,0,0.15);
    border-color: #007bff;
}

.detail-modal .image-thumbnail-right {
    width: 80px;
    height: 80px;
    object-fit: cover;
    display: block;
}

.detail-modal .image-order-right {
    position: absolute;
    top: 4px;
    left: 4px;
    background: rgba(0,0,0,0.8);
    color: white;
    border-radius: 50%;
    width: 18px;
    height: 18px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 10px;
    font-weight: bold;
}

.detail-modal .status-section {
    margin-bottom: 0;
}

.detail-modal .status-item {
    margin-bottom: 12px;
    display: flex;
    align-items: center;
    gap: 8px;
}

.detail-modal .status-item:last-child {
    margin-bottom: 0;
}

.detail-modal .rejection-history-section {
    margin-top: 25px;
    padding-top: 20px;
    border-top: 1px solid #dee2e6;
}

.detail-modal .rejection-item {
    background: #fff5f5;
    border: 1px solid #fed7d7;
    border-radius: 6px;
    padding: 12px;
    margin-bottom: 10px;
}

.detail-modal .rejection-item:last-child {
    margin-bottom: 0;
}

.detail-modal .rejection-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 8px;
}

.detail-modal .rejection-reason {
    background: white;
    padding: 8px 12px;
    border-radius: 4px;
    border-left: 3px solid #e53e3e;
    margin-bottom: 6px;
    font-size: 13px;
    line-height: 1.4;
}

.detail-modal .rejection-by {
    text-align: right;
}

.detail-modal .review-section {
    margin-top: 25px;
    padding-top: 20px;
    border-top: 1px solid #dee2e6;
}

.detail-modal .review-section .form-control {
    border-radius: 6px;
    border: 1px solid #ced4da;
    font-size: 13px;
}

.detail-modal .review-section .form-control:focus {
    border-color: #007bff;
    box-shadow: 0 0 0 0.2rem rgba(0,123,255,.25);
}

.detail-modal .review-section .btn {
    border-radius: 6px;
    font-weight: 500;
    padding: 8px 16px;
}

.detail-modal .review-section .btn-success:hover {
    background-color: #157347;
    border-color: #146c43;
}

.detail-modal .review-section .btn-danger:hover {
    background-color: #bb2d3b;
    border-color: #b02a37;
}

.detail-modal .character-count-section {
    background: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 8px;
    padding: 15px;
    margin-bottom: 20px;
}

.detail-modal .character-count-item {
    padding: 8px 0;
    border-bottom: 1px solid #e9ecef;
}

.detail-modal .character-count-item:last-child {
    border-bottom: none;
    padding-bottom: 0;
}

.detail-modal .character-count-item .badge {
    font-size: 11px;
    padding: 4px 8px;
}

/* 确保图片缩略图有正确的点击样式 */
.detail-modal .image-item-right {
    cursor: pointer;
    transition: all 0.2s ease;
}

.detail-modal .image-item-right:hover {
    transform: scale(1.05);
    box-shadow: 0 6px 20px rgba(0,0,0,0.15);
    border-color: #007bff;
}
</style>

<div class="detail-modal">
<div class="row">
    <!-- 左侧：标题 + 内容 + 话题 + @用户 + 定位 -->
    <div class="col-md-8">
        <div class="content-area">
            <!-- 文案标题 -->
            <h4 class="content-title">
                <i class="bi bi-file-text text-primary"></i>
                {{ content.title }}
            </h4>
            <!-- 隐藏的数据属性用于JavaScript -->
            <div id="titleData" data-title="{{ content.title }}" style="display: none;"></div>

            <!-- 文案内容 -->
            <div class="content-text">{{ content.content|trim }}</div>
            <!-- 隐藏的数据属性用于JavaScript -->
            <div id="contentData" data-content="{{ content.content }}" style="display: none;"></div>

            <!-- 底部信息区域 -->
            <div class="bottom-info-section">
                <!-- 话题标签 -->
                <div class="info-group">
                    <h6 class="info-title">
                        <i class="bi bi-tags text-primary"></i> 话题标签
                    </h6>
                    <div class="tags-container">
                        {% if content.topics_list %}
                            {% for topic in content.topics_list %}
                                <span class="badge bg-primary">#{{ topic }}</span>
                            {% endfor %}
                        {% else %}
                            <span class="text-muted">暂无话题标签</span>
                        {% endif %}
                    </div>
                </div>

                <!-- 位置信息 -->
                <div class="info-group">
                    <h6 class="info-title">
                        <i class="bi bi-geo-alt text-danger"></i> 位置信息
                    </h6>
                    <div class="location-info">
                        <i class="bi bi-geo-alt-fill"></i>
                        <span>{{ content.location if content.location else content.id }}</span>
                    </div>
                </div>

                <!-- @用户信息 -->
                <div class="info-group">
                    <h6 class="info-title">
                        <i class="bi bi-person text-info"></i> @用户
                    </h6>
                    <div class="user-info">
                        {% if content.at_users_list %}
                            <div class="tags-container">
                                {% for user in content.at_users_list %}
                                    <span class="badge bg-info">@{{ user }}</span>
                                {% endfor %}
                            </div>
                        {% endif %}
                        <div class="mt-2">
                            <span class="badge bg-primary">{{ content.client.name if content.client else '未知客户' }}</span>
                            <span class="text-muted small ms-2">
                                {{ content.created_at.strftime('%Y-%m-%d %H:%M') if content.created_at else '未知时间' }}
                            </span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 右侧：图片 + 状态 + 优先级 -->
    <div class="col-md-4">
        <div class="right-panel">
            <!-- 配图区域 -->
            {% if images %}
            <div class="images-section">
                <h6 class="section-title">
                    <i class="bi bi-images text-success"></i>
                    配图预览 ({{ images|length }} 张)
                </h6>
                <div class="image-grid-right">
                    {% for image in images %}
                    <div class="image-item-right"
                         onclick="showImageModal('{{ url_for('static', filename='uploads/' + image.image_path) }}', '{{ image.original_name }}')"
                         style="cursor: pointer;">
                        <img src="{{ url_for('static', filename='uploads/' + image.thumbnail_path) }}"
                             class="image-thumbnail-right"
                             title="{{ image.original_name }}"
                             alt="{{ image.original_name }}">
                        <div class="image-order-right">{{ loop.index }}</div>
                    </div>
                    {% endfor %}
                </div>
            </div>
            {% endif %}

            <!-- 状态信息 -->
            <div class="status-section">
                <h6 class="section-title">
                    <i class="bi bi-info-circle text-primary"></i>
                    状态信息
                </h6>
                <div class="status-item">
                    <strong>当前状态：</strong>
                    {% if content.workflow_status == 'draft' and content.client_review_status == 'rejected' %}
                        <span class="badge bg-danger">客户驳回</span>
                    {% elif content.workflow_status == 'draft' and content.internal_review_status == 'rejected' %}
                        <span class="badge bg-warning">内部驳回</span>
                    {% elif content.workflow_status == 'draft' %}
                        <span class="badge bg-secondary">草稿</span>
                    {% elif content.workflow_status == 'pending_review' %}
                        <span class="badge bg-warning">待初审</span>
                    {% elif content.workflow_status == 'first_reviewed' %}
                        <span class="badge bg-success">初审通过</span>
                    {% elif content.workflow_status == 'pending_image' %}
                        <span class="badge bg-info">待上传图片</span>
                    {% elif content.workflow_status == 'image_uploaded' %}
                        <span class="badge bg-info">图片已上传</span>
                    {% elif content.workflow_status == 'final_review' %}
                        <span class="badge bg-warning">待最终审核</span>
                    {% elif content.workflow_status == 'pending_client_review' %}
                        <span class="badge bg-primary">待客户审核</span>
                    {% elif content.workflow_status == 'pending_publish' %}
                        <span class="badge bg-info">待发布</span>
                    {% elif content.workflow_status == 'published' %}
                        <span class="badge bg-success">已发布</span>
                    {% else %}
                        <span class="badge bg-secondary">{{ content.workflow_status }}</span>
                    {% endif %}
                </div>
                <div class="status-item">
                    <strong>优先级：</strong>
                    {% if content.publish_priority == 'high' %}
                        <span class="badge bg-danger">高</span>
                    {% elif content.publish_priority == 'normal' %}
                        <span class="badge bg-primary">普通</span>
                    {% elif content.publish_priority == 'low' %}
                        <span class="badge bg-secondary">低</span>
                    {% else %}
                        <span class="badge bg-secondary">{{ content.publish_priority or '普通' }}</span>
                    {% endif %}
                </div>
            </div>

            <!-- 驳回历史 - 只在驳回状态时显示 -->
            {% set is_rejected = (content.client_review_status == 'rejected') or
                                (content.internal_review_status in ['rejected', 'final_rej_text', 'final_rej_img', 'final_rej_both', 'final_rej_text_ok', 'final_rej_img_ok', 'client_rej_text', 'client_rej_img', 'client_rej_both']) %}
            {% if rejections and is_rejected %}
            <div class="rejection-history-section">
                <h6 class="section-title">
                    <i class="bi bi-exclamation-triangle text-danger"></i>
                    驳回历史
                </h6>
                {% for rejection in rejections %}
                <div class="rejection-item">
                    <div class="rejection-header">
                        <span class="badge bg-danger">
                            {% if rejection.is_client %}
                                客户驳回
                            {% else %}
                                内部驳回
                            {% endif %}
                        </span>
                        <small class="text-muted">
                            {{ rejection.created_at.strftime('%Y-%m-%d %H:%M') }}
                        </small>
                    </div>
                    <div class="rejection-reason">
                        {{ rejection.reason }}
                    </div>
                    {% if rejection.creator %}
                    <div class="rejection-by">
                        <small class="text-muted">
                            驳回人：{{ rejection.creator.username }}
                        </small>
                    </div>
                    {% endif %}
                </div>
                {% endfor %}
            </div>
            {% endif %}

            <!-- 审核操作 -->
            <div class="review-section">
                <h6 class="section-title">
                    <i class="bi bi-clipboard-check text-success"></i>
                    审核操作
                </h6>

                <!-- 字符统计显示区域 -->
                <div class="character-count-section mb-3">
                    <h6 class="section-title">
                        <i class="bi bi-type text-info"></i>
                        字符统计
                    </h6>
                    <div class="character-count-item mb-2">
                        <div class="d-flex justify-content-end align-items-center">
                            <div class="d-flex align-items-center gap-2">
                                <span class="badge bg-light text-dark">
                                    <span id="viewTitleLength">{{ title_length or 0 }}</span>/20
                                </span>
                                <span id="viewTitleStatus" class="badge bg-{{ 'success' if title_status == 'success' else 'danger' }}">
                                    {% if title_status == 'success' %}
                                        <i class="bi bi-check-circle me-1"></i>标题符合
                                    {% else %}
                                        <i class="bi bi-exclamation-triangle me-1"></i>标题不符合
                                    {% endif %}
                                </span>
                            </div>
                        </div>
                    </div>
                    <div class="character-count-item">
                        <div class="d-flex justify-content-end align-items-center">
                            <div class="d-flex align-items-center gap-2">
                                <span class="badge bg-light text-dark">
                                    <span id="viewContentLength">{{ content_length or 0 }}</span>/1000
                                </span>
                                <span id="viewContentStatus" class="badge bg-{{ 'success' if content_status == 'success' else 'danger' }}">
                                    {% if content_status == 'success' %}
                                        <i class="bi bi-check-circle me-1"></i>内容符合
                                    {% else %}
                                        <i class="bi bi-exclamation-triangle me-1"></i>内容不符合
                                    {% endif %}
                                </span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 审核按钮 -->
                <div class="d-grid gap-2">
                    <button type="button" class="btn btn-success btn-sm" id="approveButton"
                            onclick="approveContentInModal({{ content.id }})">
                        <i class="bi bi-check-circle"></i> 通过审核
                    </button>
                    <button type="button" class="btn btn-danger btn-sm"
                            onclick="deleteContentInModal({{ content.id }})">
                        <i class="bi bi-trash"></i> 删除文案
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>
</div>



<script>
// 查看详情模态框已加载
console.log('查看详情模态框已加载');
console.log('全局函数检查:');
console.log('approveContentInModal:', typeof window.approveContentInModal);
console.log('rejectContentInModal:', typeof window.rejectContentInModal);

// 字符统计函数 - 与其他地方保持一致
function calculateTitleLength(text) {
    let length = 0;
    for (let char of text) {
        if (char.codePointAt(0) > 127) {
            // 检查是否是中文字符
            if (/[\u4e00-\u9fff]/.test(char)) {
                length += 1; // 中文字符算1个
            } else {
                length += 1; // emoji算1个
            }
        } else {
            // 字母数字算0.5个
            length += 0.5;
        }
    }
    return Math.ceil(length); // 向上取整
}

function calculateContentLength(text) {
    let length = 0;
    for (let char of text) {
        // 检查是否是中文字符
        if (/[\u4e00-\u9fff]/.test(char)) {
            length += 2; // 中文字符算2个
        } else {
            length += 1; // 其他字符算1个
        }
    }
    return length;
}

// 初始化字符统计
function initializeViewCharacterCount() {
    console.log('=== 初始化查看详情字符统计 ===');
    
    // 从隐藏的数据属性获取文本内容
    const titleDataElement = document.getElementById('titleData');
    const contentDataElement = document.getElementById('contentData');
    
    console.log('标题数据元素:', titleDataElement);
    console.log('内容数据元素:', contentDataElement);
    
    if (!titleDataElement || !contentDataElement) {
        console.error('找不到标题或内容数据元素');
        return;
    }
    
    // 获取文本内容
    const titleText = titleDataElement.getAttribute('data-title') || '';
    const contentText = contentDataElement.getAttribute('data-content') || '';
    
    console.log('标题文本:', titleText);
    console.log('内容文本:', contentText);
    
    // 计算标题长度
    const titleLength = calculateTitleLength(titleText);
    const titleLengthElement = document.getElementById('viewTitleLength');
    const titleStatusElement = document.getElementById('viewTitleStatus');
    
    console.log('标题长度计算结果:', titleLength);
    console.log('标题长度元素:', titleLengthElement);
    console.log('标题状态元素:', titleStatusElement);
    
    if (titleLengthElement && titleStatusElement) {
        titleLengthElement.textContent = titleLength;
        
        if (titleLength > 20) {
            titleStatusElement.className = 'badge bg-danger';
            titleStatusElement.innerHTML = '<i class="bi bi-exclamation-triangle me-1"></i>标题不符合';
        } else {
            titleStatusElement.className = 'badge bg-success';
            titleStatusElement.innerHTML = '<i class="bi bi-check-circle me-1"></i>标题符合';
        }
    }
    
    // 计算内容长度
    const contentLength = calculateContentLength(contentText);
    const contentLengthElement = document.getElementById('viewContentLength');
    const contentStatusElement = document.getElementById('viewContentStatus');
    
    console.log('内容长度计算结果:', contentLength);
    console.log('内容长度元素:', contentLengthElement);
    console.log('内容状态元素:', contentStatusElement);
    
    if (contentLengthElement && contentStatusElement) {
        contentLengthElement.textContent = contentLength;
        
        if (contentLength > 1000) {
            contentStatusElement.className = 'badge bg-danger';
            contentStatusElement.innerHTML = '<i class="bi bi-exclamation-triangle me-1"></i>内容不符合';
        } else {
            contentStatusElement.className = 'badge bg-success';
            contentStatusElement.innerHTML = '<i class="bi bi-check-circle me-1"></i>内容符合';
        }
    }
    
    // 根据字符统计结果控制审核按钮状态
    const approveButton = document.getElementById('approveButton');
    if (approveButton) {
        const isOverLimit = titleLength > 20 || contentLength > 1000;

        if (isOverLimit) {
            // 超出限制，禁用按钮
            approveButton.disabled = true;
            approveButton.className = 'btn btn-secondary btn-sm';
            approveButton.title = '内容长度超出限制，无法通过审核';
            approveButton.innerHTML = '<i class="bi bi-x-circle"></i> 无法通过';
            console.log('❌ 审核按钮已禁用 - 长度超出限制');
        } else {
            // 符合要求，启用按钮
            approveButton.disabled = false;
            approveButton.className = 'btn btn-success btn-sm';
            approveButton.title = '通过审核';
            approveButton.innerHTML = '<i class="bi bi-check-circle"></i> 通过审核';
            console.log('✅ 审核按钮已启用 - 长度符合要求');
        }
    }

    console.log('字符统计完成:', {
        titleLength,
        contentLength,
        titleElement: !!titleLengthElement,
        contentElement: !!contentLengthElement
    });
}

// 直接执行字符统计（多次重试确保成功）
function executeCharacterCount() {
    console.log('=== 执行字符统计 ===', new Date().toLocaleTimeString());

    // 获取数据元素
    const titleDataElement = document.getElementById('titleData');
    const contentDataElement = document.getElementById('contentData');

    console.log('titleData元素:', titleDataElement);
    console.log('contentData元素:', contentDataElement);

    if (!titleDataElement || !contentDataElement) {
        console.log('数据元素未找到，稍后重试...');
        return false;
    }

    // 获取文本内容
    const titleText = titleDataElement.getAttribute('data-title') || '';
    const contentText = contentDataElement.getAttribute('data-content') || '';

    console.log('标题文本:', titleText);
    console.log('内容文本:', contentText);

    if (!titleText && !contentText) {
        console.log('文本内容为空，稍后重试...');
        return false;
    }

    // 计算字符长度
    const titleLength = calculateTitleLength(titleText);
    const contentLength = calculateContentLength(contentText);

    console.log('标题长度:', titleLength);
    console.log('内容长度:', contentLength);

    // 更新显示
    const titleLengthElement = document.getElementById('viewTitleLength');
    const titleStatusElement = document.getElementById('viewTitleStatus');
    const contentLengthElement = document.getElementById('viewContentLength');
    const contentStatusElement = document.getElementById('viewContentStatus');

    if (titleLengthElement) {
        titleLengthElement.textContent = titleLength;
        console.log('标题长度已更新:', titleLength);
    }

    if (titleStatusElement) {
        if (titleLength > 20) {
            titleStatusElement.className = 'badge bg-danger';
            titleStatusElement.innerHTML = '<i class="bi bi-exclamation-triangle me-1"></i>标题不符合';
        } else {
            titleStatusElement.className = 'badge bg-success';
            titleStatusElement.innerHTML = '<i class="bi bi-check-circle me-1"></i>标题符合';
        }
    }

    if (contentLengthElement) {
        contentLengthElement.textContent = contentLength;
        console.log('内容长度已更新:', contentLength);
    }

    if (contentStatusElement) {
        if (contentLength > 1000) {
            contentStatusElement.className = 'badge bg-danger';
            contentStatusElement.innerHTML = '<i class="bi bi-exclamation-triangle me-1"></i>内容不符合';
        } else {
            contentStatusElement.className = 'badge bg-success';
            contentStatusElement.innerHTML = '<i class="bi bi-check-circle me-1"></i>内容符合';
        }
    }

    // 根据字符统计结果控制审核按钮状态
    const approveButton = document.getElementById('approveButton');
    if (approveButton) {
        const isOverLimit = titleLength > 20 || contentLength > 1000;

        if (isOverLimit) {
            // 超出限制，禁用按钮
            approveButton.disabled = true;
            approveButton.className = 'btn btn-secondary btn-sm';
            approveButton.title = '内容长度超出限制，无法通过审核';
            approveButton.innerHTML = '<i class="bi bi-x-circle"></i> 无法通过';
            console.log('❌ 审核按钮已禁用 - 长度超出限制');
        } else {
            // 符合要求，启用按钮
            approveButton.disabled = false;
            approveButton.className = 'btn btn-success btn-sm';
            approveButton.title = '通过审核';
            approveButton.innerHTML = '<i class="bi bi-check-circle"></i> 通过审核';
            console.log('✅ 审核按钮已启用 - 长度符合要求');
        }
    }

    console.log('✅ 字符统计执行完成');
    return true;
}

// 多次重试执行
let retryCount = 0;
const maxRetries = 10;

function retryCharacterCount() {
    retryCount++;
    console.log(`尝试执行字符统计 (${retryCount}/${maxRetries})`);

    if (executeCharacterCount()) {
        console.log('✅ 字符统计成功完成');
        return;
    }

    if (retryCount < maxRetries) {
        setTimeout(retryCharacterCount, 200 * retryCount); // 递增延迟
    } else {
        console.error('❌ 字符统计执行失败，已达到最大重试次数');
    }
}

</script>
