<!-- 客户文案初审页面内容 -->
<div class="container-fluid py-4">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h2 class="mb-0">
                <i class="bi bi-clipboard-check text-primary"></i> {{ client.name }} - 初审文案
                <small class="text-muted fs-6">审核该客户的文案内容</small>
            </h2>
            <nav aria-label="breadcrumb" class="mt-2">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item">
                        <a href="{{ url_for('main_simple.review_content_page') }}">初审文案</a>
                    </li>
                    <li class="breadcrumb-item active" aria-current="page">{{ client.name }}</li>
                </ol>
            </nav>
        </div>
        <div>
            <a href="{{ url_for('main_simple.review_content_page') }}" class="btn btn-secondary">
                <i class="bi bi-arrow-left"></i> 返回客户列表
            </a>
        </div>
    </div>

    <div class="card mb-4">
        <div class="card-header bg-light">
            <h6 class="mb-3"><i class="bi bi-funnel"></i> 筛选条件</h6>
            <form method="get" class="row g-3" id="filterForm">

                <div class="col-md-2">
                    <div class="form-group">
                        <label class="form-label">任务</label>
                        <select name="task_id" id="task_id" class="form-select">
                            <option value="0">全部任务</option>
                            {% for task in tasks %}
                            <option value="{{ task.id }}" {% if task.id == task_id %}selected{% endif %}>{{ task.name }}</option>
                            {% endfor %}
                        </select>
                    </div>
                </div>
                <div class="col-md-2">
                    <div class="form-group">
                        <label class="form-label">状态</label>
                        <select name="status" id="status" class="form-select">
                            <option value="">全部状态</option>
                            <option value="draft" {% if status == 'draft' %}selected{% endif %}>草稿</option>
                            <option value="pending_review" {% if status == 'pending_review' %}selected{% endif %}>内部驳回</option>
                            <option value="client_rejected" {% if status == 'client_rejected' %}selected{% endif %}>客户驳回</option>
                        </select>
                    </div>
                </div>

                <div class="col-md-2">
                    <div class="form-group">
                        <label class="form-label">搜索</label>
                        <input type="text" name="search" id="search" class="form-control" value="{{ search }}" placeholder="搜索标题或内容">
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="form-group">
                        <label class="form-label">&nbsp;</label>
                        <div class="d-flex gap-2">
                            <button type="submit" class="btn btn-primary flex-fill">
                                <i class="bi bi-search"></i> 筛选
                            </button>
                            <button type="button" class="btn btn-outline-secondary flex-fill" onclick="resetFilters()">
                                <i class="bi bi-arrow-clockwise"></i> 重置
                            </button>
                        </div>
                    </div>
                </div>
            </form>

            <!-- 状态统计卡片 -->
            <div class="row mt-4 mb-3">
                <div class="col-md-4">
                    <div class="card text-center cursor-pointer status-card" onclick="filterByStatus('draft')" data-status="draft">
                        <div class="card-body py-3">
                            <h4 class="text-secondary mb-1" id="draft-count">{{ status_counts.draft or 0 }}</h4>
                            <small class="text-muted">草稿</small>
                        </div>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="card text-center cursor-pointer status-card" onclick="filterByStatus('rejected')" data-status="rejected">
                        <div class="card-body py-3">
                            <h4 class="text-danger mb-1" id="rejected-count">{{ (status_counts.pending_review or 0) + (status_counts.client_rejected or 0) }}</h4>
                            <small class="text-muted">被驳回</small>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 批量操作区域 -->
            <div class="mt-3 pt-3 border-top">
                <div class="d-flex align-items-center gap-3">
                    <span class="text-muted">
                        <i class="bi bi-info-circle"></i>
                        已选择 <span id="selectedCount">0</span> 篇文案
                    </span>
                    <button type="button" class="btn btn-success btn-sm batch-review-btn" data-action="approve" style="display: none;">
                        <i class="bi bi-check"></i> 批量通过
                    </button>
                    <button type="button" class="btn btn-danger btn-sm batch-delete-btn" style="display: none;">
                        <i class="bi bi-trash"></i> 批量删除
                    </button>
                </div>
            </div>
        </div>
    </div>

    <div class="card">
        <div class="card-body p-0">
            {% if contents %}
                <div class="table-responsive">
                    <table class="table table-hover table-striped mb-0">
                        <thead class="table-light">
                            <tr>
                                <th width="40px">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="selectAll">
                                    </div>
                                </th>
                                <th width="60px">ID</th>
                                <th width="180px">标题</th>
                                <th width="100px">客户</th>
                                <th width="120px">任务</th>
                                <th width="100px">状态</th>
                                <th width="80px">长度</th>
                                <th width="85px">创建时间</th>
                                <th width="320px">操作</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for content in contents %}
                            <tr data-content-id="{{ content.id }}">
                                <td>
                                    <div class="form-check">
                                        <input class="form-check-input content-checkbox" type="checkbox" value="{{ content.id }}">
                                    </div>
                                </td>
                                <td>{{ content.id }}</td>
                                <td>
                                    <span class="text-decoration-none">
                                        {{ content.title[:50] }}{% if content.title|length > 50 %}...{% endif %}
                                    </span>
                                </td>
                                <td>
                                    <span class="badge bg-light text-dark">{{ content.client.name if content.client else '-' }}</span>
                                </td>
                                <td>
                                    <span class="badge bg-info">{{ content.task.name if content.task else '-' }}</span>
                                </td>
                                <td>
                                    {% if content.workflow_status == 'draft' and content.client_review_status == 'rejected' %}
                                        {# 客户驳回的文案 #}
                                        <span class="badge bg-danger">客户驳回</span>
                                    {% elif content.workflow_status == 'draft' and content.internal_review_status == 'rejected' %}
                                        {# 内部驳回的文案 #}
                                        <span class="badge bg-warning">内部驳回</span>
                                    {% elif content.workflow_status == 'draft' and content.internal_review_status in ['final_rej_both', 'final_rej_text'] %}
                                        {# 最终审核驳回的文案 #}
                                        <span class="badge bg-warning">内部驳回</span>
                                    {% elif content.workflow_status == 'draft' and content.internal_review_status == 'final_rej_text_ok' %}
                                        {# 最终审核驳回文案问题但图片已修复 #}
                                        <span class="badge bg-warning">内部驳回</span>
                                    {% elif content.workflow_status == 'draft' %}
                                        {# 普通草稿 #}
                                        <span class="badge bg-secondary">草稿</span>
                                    {% elif content.workflow_status == 'pending_review' and content.internal_review_status == 'final_rej_both_img' %}
                                        {# 最终审核驳回（图片已完成，文案待编辑） #}
                                        <span class="badge bg-warning">内部驳回</span>
                                    {% elif content.workflow_status == 'pending_review' and content.internal_review_status in ['rejected', 'final_rej_text', 'final_rej_both', 'final_rej_text_ok'] %}
                                        {# 真正被驳回的内容 #}
                                        <span class="badge bg-warning">内部驳回</span>
                                    {% elif content.workflow_status == 'pending_review' %}
                                        {# 新生成的待初审内容 #}
                                        <span class="badge bg-info">待初审</span>
                                    {% elif content.workflow_status == 'first_reviewed' %}
                                        <span class="badge bg-success">初审通过</span>
                                    {% elif content.workflow_status == 'pending_image' %}
                                        <span class="badge bg-info">待上传图片</span>
                                    {% elif content.workflow_status == 'image_uploaded' %}
                                        <span class="badge bg-info">图片已上传</span>
                                    {% elif content.workflow_status == 'pending_final_review' %}
                                        <span class="badge bg-warning">待最终审核</span>
                                    {% elif content.workflow_status == 'pending_client_review' %}
                                        <span class="badge bg-primary">待客户审核</span>
                                    {% elif content.workflow_status == 'pending_publish' %}
                                        <span class="badge bg-info">待发布</span>
                                    {% elif content.workflow_status == 'published' %}
                                        <span class="badge bg-success">已发布</span>
                                    {% else %}
                                        <span class="badge bg-secondary">{{ content.workflow_status }}</span>
                                    {% endif %}
                                </td>
                                <td>
                                    <span class="length-badge" data-title="{{ content.title or '' }}" data-content="{{ content.content or '' }}">
                                        <i class="bi bi-hourglass"></i> 计算中...
                                    </span>
                                </td>
                                <td>
                                    <small>{{ content.created_at.strftime('%m-%d %H:%M') }}</small>
                                </td>
                                <td>
                                    <div class="btn-group btn-group-sm" role="group">
                                        <button type="button" class="btn btn-outline-info btn-sm px-2" title="查看详情" onclick="viewContent({{ content.id }})">
                                            <i class="bi bi-eye me-1"></i><span class="d-none d-lg-inline">查看</span>
                                        </button>

                                        {% if content.workflow_status == 'draft' %}
                                                <button type="button" class="btn btn-outline-success btn-sm px-2 review-btn"
                                                        data-content-id="{{ content.id }}"
                                                        data-action="approve"
                                                        data-title="{{ content.title or '' }}"
                                                        data-content="{{ content.content or '' }}"
                                                        title="通过初审">
                                                    <i class="bi bi-check me-1"></i><span class="d-none d-lg-inline">通过</span>
                                                </button>
                                        <button type="button" class="btn btn-outline-danger btn-sm px-2 delete-btn"
                                                data-content-id="{{ content.id }}"
                                                title="删除文案">
                                            <i class="bi bi-trash me-1"></i><span class="d-none d-lg-inline">删除</span>
                                        </button>
                                        {% endif %}

                                        {% if content.workflow_status == 'draft' %}
                                        <button type="button" class="btn btn-outline-primary btn-sm px-2" title="编辑" onclick="editContent({{ content.id }})">
                                            <i class="bi bi-pencil me-1"></i><span class="d-none d-lg-inline">编辑</span>
                                        </button>
                                        {% endif %}
                                    </div>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            {% else %}
                <div class="text-center py-5">
                    <div class="text-muted">
                        <i class="bi bi-inbox display-1 mb-3"></i>
                        <h5 class="mb-2">暂无待初审的文案</h5>
                        <p class="mb-3">该客户的所有文案都已审核完成</p>
                        <p class="mb-0">
                            <span id="redirectCountdown">3</span> 秒后自动返回客户列表，或
                            <a href="{{ url_for('main_simple.review_content_page') }}" class="btn btn-primary btn-sm">
                                <i class="bi bi-arrow-left"></i> 立即返回
                            </a>
                        </p>
                    </div>
                </div>
                <script>
                // 自动跳转倒计时
                let countdown = 3;
                const countdownElement = document.getElementById('redirectCountdown');

                const timer = setInterval(() => {
                    countdown--;
                    if (countdownElement) {
                        countdownElement.textContent = countdown;
                    }

                    if (countdown <= 0) {
                        clearInterval(timer);
                        // 跳转到客户列表页面
                        window.location.href = '{{ url_for("main_simple.review_content_page") }}';
                    }
                }, 1000);
                </script>
            {% endif %}
        </div>
    </div>

    <!-- 分页 -->
    {% if pagination and pagination.pages > 1 %}
    <nav class="mt-4">
        <ul class="pagination justify-content-center mb-0">
            {% if pagination.has_prev %}
            <li class="page-item">
                <a class="page-link" href="?page={{ pagination.prev_num }}&client_id={{ client_id }}&task_id={{ task_id }}&batch_id={{ batch_id }}&status={{ status }}&search={{ search }}">
                    <span>&laquo;</span>
                </a>
            </li>
            {% else %}
            <li class="page-item disabled">
                <span class="page-link">&laquo;</span>
            </li>
            {% endif %}
            
            {% for page in pagination.iter_pages() %}
                {% if page %}
                    {% if page != pagination.page %}
                    <li class="page-item">
                        <a class="page-link" href="?page={{ page }}&client_id={{ client_id }}&task_id={{ task_id }}&batch_id={{ batch_id }}&status={{ status }}&search={{ search }}">{{ page }}</a>
                    </li>
                    {% else %}
                    <li class="page-item active">
                        <span class="page-link">{{ page }}</span>
                    </li>
                    {% endif %}
                {% else %}
                <li class="page-item disabled">
                    <span class="page-link">...</span>
                </li>
                {% endif %}
            {% endfor %}
            
            {% if pagination.has_next %}
            <li class="page-item">
                <a class="page-link" href="?page={{ pagination.next_num }}&client_id={{ client_id }}&task_id={{ task_id }}&batch_id={{ batch_id }}&status={{ status }}&search={{ search }}">
                    <span>&raquo;</span>
                </a>
            </li>
            {% else %}
            <li class="page-item disabled">
                <span class="page-link">&raquo;</span>
            </li>
            {% endif %}
        </ul>
    </nav>
    {% endif %}
</div>

<!-- JavaScript -->
<script>
// 全选/取消全选
const selectAllCheckbox = document.getElementById('selectAll');
if (selectAllCheckbox) {
    selectAllCheckbox.addEventListener('change', function() {
        const checkboxes = document.querySelectorAll('.content-checkbox');
        checkboxes.forEach(checkbox => {
            // 只操作未被禁用的复选框
            if (!checkbox.disabled) {
                checkbox.checked = this.checked;
            }
        });
        updateSelectedCount();
    });
}

// 单个复选框变化
document.addEventListener('change', function(e) {
    if (e.target.classList.contains('content-checkbox')) {
        updateSelectedCount();
    }
});

// 更新复选框状态（禁用超长文案的复选框）
function updateCheckboxStates() {
    console.log('=== 更新复选框状态 ===');

    const contentRows = document.querySelectorAll('tr[data-content-id]');
    console.log('找到文案行数:', contentRows.length);

    contentRows.forEach(function(row) {
        const contentId = row.getAttribute('data-content-id');
        const checkbox = row.querySelector('.content-checkbox');
        const reviewBtn = row.querySelector('.review-btn');
        const lengthBadge = row.querySelector('.length-badge') || row.querySelector('[class*="badge"]');

        // 更详细的调试信息
        const allBadges = row.querySelectorAll('[class*="badge"]');
        const badgeTexts = Array.from(allBadges).map(b => b.textContent.trim());

        console.log(`检查文案 ${contentId}:`, {
            checkbox: checkbox ? '存在' : '不存在',
            reviewBtn: reviewBtn ? '存在' : '不存在',
            reviewBtnDisabled: reviewBtn ? reviewBtn.disabled : 'N/A',
            lengthBadge: lengthBadge ? '存在' : '不存在',
            lengthBadgeText: lengthBadge ? lengthBadge.textContent.trim() : 'N/A',
            allBadges: badgeTexts,
            rowHTML: row.innerHTML.substring(0, 200) + '...'
        });

        if (checkbox) {
            let shouldDisable = false;

            // 方法1：检查审核按钮是否被禁用
            if (reviewBtn && reviewBtn.disabled) {
                shouldDisable = true;
                console.log(`文案 ${contentId}: 审核按钮被禁用`);
            }

            // 方法2：检查长度标记是否显示"超长"
            if (lengthBadge && lengthBadge.textContent.includes('超长')) {
                shouldDisable = true;
                console.log(`文案 ${contentId}: 长度标记显示超长`);
            }

            // 方法2.1：检查行中所有包含"超长"的元素
            const allBadges = row.querySelectorAll('[class*="badge"]');
            allBadges.forEach(badge => {
                if (badge.textContent.includes('超长')) {
                    shouldDisable = true;
                    console.log(`文案 ${contentId}: 找到超长标记 - ${badge.textContent.trim()}`);
                }
            });

            // 方法2.2：检查整行文本是否包含"超长"
            if (row.textContent.includes('超长')) {
                shouldDisable = true;
                console.log(`文案 ${contentId}: 行文本包含超长`);
            }

            // 方法3：直接检查内容长度
            const titleText = reviewBtn ? reviewBtn.getAttribute('data-title') || '' : '';
            const contentText = reviewBtn ? reviewBtn.getAttribute('data-content') || '' : '';

            if (titleText || contentText) {
                const titleLength = calculateTitleLength(titleText);
                const contentLength = calculateContentLength(contentText);
                const isOverLimit = titleLength > 20 || contentLength > 1000;

                if (isOverLimit) {
                    shouldDisable = true;
                    console.log(`文案 ${contentId}: 内容长度超限 (标题:${titleLength}/20, 内容:${contentLength}/1000)`);
                }
            }

            if (shouldDisable) {
                console.log(`禁用文案 ${contentId} 的复选框`);
                // 如果已选中，取消选中
                if (checkbox.checked) {
                    checkbox.checked = false;
                }

                // 强制禁用复选框
                checkbox.disabled = true;
                checkbox.style.opacity = '0.3';
                checkbox.style.cursor = 'not-allowed';
                checkbox.title = '内容长度超出限制，无法批量操作';

                // 移除所有现有的事件监听器并添加新的阻止事件
                const newCheckbox = checkbox.cloneNode(true);
                checkbox.parentNode.replaceChild(newCheckbox, checkbox);

                // 为新的复选框添加阻止事件
                newCheckbox.addEventListener('click', function(e) {
                    e.preventDefault();
                    e.stopPropagation();
                    this.checked = false;
                    console.log('阻止超长文案复选框被选中');
                    return false;
                }, true);

                newCheckbox.addEventListener('change', function(e) {
                    e.preventDefault();
                    e.stopPropagation();
                    this.checked = false;
                    console.log('阻止超长文案复选框状态改变');
                    return false;
                }, true);

            } else {
                console.log(`启用文案 ${contentId} 的复选框`);
                checkbox.disabled = false;
                checkbox.style.opacity = '';
                checkbox.style.cursor = '';
                checkbox.title = '';
            }
        }
    });

    // 更新选中数量（因为可能有复选框被禁用和取消选中）
    updateSelectedCount();
    console.log('=== 复选框状态更新完成 ===');
}

// 更新选中数量
function updateSelectedCount() {
    const checkboxes = document.querySelectorAll('.content-checkbox:checked');
    const count = checkboxes.length;

    const selectedCountElement = document.getElementById('selectedCount');
    if (selectedCountElement) {
        selectedCountElement.textContent = count;
    }

    // 显示/隐藏批量操作按钮
    const batchReviewBtn = document.querySelector('.batch-review-btn');
    const batchDeleteBtn = document.querySelector('.batch-delete-btn');

    if (batchReviewBtn) {
        batchReviewBtn.style.display = count > 0 ? 'inline-block' : 'none';
    }
    if (batchDeleteBtn) {
        batchDeleteBtn.style.display = count > 0 ? 'inline-block' : 'none';
    }

    // 更新全选复选框状态（只考虑未被禁用的复选框）
    const allEnabledCheckboxes = Array.from(document.querySelectorAll('.content-checkbox')).filter(cb => !cb.disabled);
    const selectAllCheckbox = document.getElementById('selectAll');
    if (selectAllCheckbox) {
        if (count === 0) {
            selectAllCheckbox.indeterminate = false;
            selectAllCheckbox.checked = false;
        } else if (count === allEnabledCheckboxes.length) {
            selectAllCheckbox.indeterminate = false;
            selectAllCheckbox.checked = true;
        } else {
            selectAllCheckbox.indeterminate = true;
        }
    }
}



// 查看文案详情
window.viewContent = function(contentId) {
    // 使用AJAX加载文案详情
    fetch(`/simple/api/contents/${contentId}/view`, {
        method: 'GET',
        headers: {
            'X-Requested-With': 'XMLHttpRequest'
        }
    })
    .then(response => response.text())
    .then(html => {
        // 创建模态框显示详情
        showContentModal('查看文案详情', html);
    })
    .catch(error => {
        console.error('加载文案详情失败:', error);
        alert('加载文案详情失败，请重试');
    });
}

// 重置筛选条件
function resetFilters() {
    // 重置所有筛选条件
    document.getElementById('client_id').value = '0';
    document.getElementById('task_id').value = '0';
    document.getElementById('search').value = '';

    // 提交表单以应用重置
    document.getElementById('filterForm').submit();
}

// 客户文案审核页面 - 不需要客户联动功能

// 编辑文案
window.editContent = function(contentId) {
    // 使用AJAX加载编辑表单
    fetch(`/simple/api/contents/${contentId}/edit`, {
        method: 'GET',
        headers: {
            'X-Requested-With': 'XMLHttpRequest'
        }
    })
    .then(response => response.text())
    .then(html => {
        // 创建模态框显示编辑表单
        showContentModal('编辑文案', html);
    })
    .catch(error => {
        console.error('加载编辑表单失败:', error);
        alert('加载编辑表单失败，请重试');
    });
}

// 显示内容模态框
function showContentModal(title, content) {
    // 创建模态框HTML
    const modalHtml = `
        <div class="modal fade" id="contentModal" tabindex="-1" aria-labelledby="contentModalLabel" aria-hidden="true">
            <div class="modal-dialog modal-lg">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title" id="contentModalLabel">${title}</h5>
                    </div>
                    <div class="modal-body">
                        ${content}
                    </div>
                </div>
            </div>
        </div>
    `;

    // 移除已存在的模态框
    const existingModal = document.getElementById('contentModal');
    if (existingModal) {
        existingModal.remove();
    }

    // 添加新模态框到页面
    document.body.insertAdjacentHTML('beforeend', modalHtml);

    // 显示模态框
    const modal = new bootstrap.Modal(document.getElementById('contentModal'));
    modal.show();

    // 根据模态框类型进行初始化
    setTimeout(function() {
        if (title.includes('编辑')) {
            console.log('检测到编辑模态框，开始初始化...');
            // 直接调用初始化逻辑，不重复执行脚本
            initializeEditModalDirectly();
        } else if (title.includes('查看')) {
            console.log('检测到查看详情模态框，开始初始化字符统计...');
            // 初始化查看详情的字符统计，使用多次重试机制
            setTimeout(() => initializeViewCharacterCount(), 100);
            setTimeout(() => initializeViewCharacterCount(), 500);
            setTimeout(() => initializeViewCharacterCount(), 1000);
        }
    }, 300);

    // 模态框关闭后移除DOM元素
    document.getElementById('contentModal').addEventListener('hidden.bs.modal', function() {
        this.remove();
    });
}

// 直接初始化编辑模态框
function initializeEditModalDirectly() {
    console.log('开始直接初始化编辑模态框...');

    // 初始化标签
    initializeEditTags();

    // 设置输入框事件
    setupEditInputEvents();

    // 设置表单提交事件
    setupEditFormSubmit();
    
    // 初始化字符统计
    setTimeout(() => {
        console.log('初始化字符统计...');
        initializeCharacterCount();
    }, 100);
}

// 字符统计函数 - 直接使用字符串长度
function calculateTitleLength(text) {
    return text.length;
}

function calculateContentLength(text) {
    return text.length;
}

// 更新标题字符统计
function updateTitleLength() {
    console.log('=== updateTitleLength 被调用 ===');
    const titleInput = document.getElementById('editTitle');
    const titleLength = document.getElementById('titleLength');
    const titleStatus = document.getElementById('titleStatus');
    
    console.log('标题元素检查:', {
        titleInput: titleInput ? '找到' : '未找到',
        titleLength: titleLength ? '找到' : '未找到',
        titleStatus: titleStatus ? '找到' : '未找到'
    });
    
    if (titleInput && titleLength && titleStatus) {
        const text = titleInput.value;
        const length = calculateTitleLength(text);
        
        console.log('编辑模态框标题统计:', { text, length, originalLength: text.length });
        
        titleLength.textContent = length;
        
        if (length > 20) {
            titleLength.className = 'text-danger';
            titleStatus.className = 'text-danger';
            titleStatus.textContent = '✗ 超出限制';
        } else {
            titleLength.className = 'text-success';
            titleStatus.className = 'text-success';
            titleStatus.textContent = '✓ 符合要求';
        }
    } else {
        console.error('编辑模态框找不到标题相关元素:', { titleInput, titleLength, titleStatus });
    }
}

// 更新内容字符统计
function updateContentLength() {
    console.log('=== updateContentLength 被调用 ===');
    const contentInput = document.getElementById('editContent');
    const contentLength = document.getElementById('contentLength');
    const contentStatus = document.getElementById('contentStatus');
    
    console.log('内容元素检查:', {
        contentInput: contentInput ? '找到' : '未找到',
        contentLength: contentLength ? '找到' : '未找到',
        contentStatus: contentStatus ? '找到' : '未找到'
    });
    
    if (contentInput && contentLength && contentStatus) {
        const text = contentInput.value;
        const length = calculateContentLength(text);
        
        console.log('编辑模态框内容统计:', { text, length, originalLength: text.length });
        
        contentLength.textContent = length;
        
        if (length > 1000) {
            contentLength.className = 'text-danger';
            contentStatus.className = 'text-danger';
            contentStatus.textContent = '✗ 超出限制';
        } else {
            contentLength.className = 'text-success';
            contentStatus.className = 'text-success';
            contentStatus.textContent = '✓ 符合要求';
        }
    } else {
        console.error('编辑模态框找不到内容相关元素:', { contentInput, contentLength, contentStatus });
    }
}

// 初始化字符统计
function initializeCharacterCount() {
    console.log('=== 初始化字符统计 ===');
    
    // 获取标题和内容输入框
    const titleInput = document.getElementById('editTitle');
    const contentInput = document.getElementById('editContent');
    
    if (titleInput && contentInput) {
        // 立即计算一次
        updateTitleLength();
        updateContentLength();
        
        // 添加输入事件监听器
        titleInput.addEventListener('input', updateTitleLength);
        contentInput.addEventListener('input', updateContentLength);
        
        console.log('字符统计初始化完成');
    } else {
        console.log('找不到编辑输入框，可能是查看详情模态框');
    }
}

// 初始化查看详情的字符统计
function initializeViewCharacterCount() {
    console.log('=== 初始化查看详情字符统计 ===');
    console.log('当前时间:', new Date().toLocaleTimeString());

    // 检查模态框是否存在
    const modal = document.getElementById('contentModal');
    console.log('模态框元素:', modal);

    // 从隐藏的数据属性获取文本内容
    const titleDataElement = document.getElementById('titleData');
    const contentDataElement = document.getElementById('contentData');

    console.log('标题数据元素:', titleDataElement);
    console.log('内容数据元素:', contentDataElement);

    if (!titleDataElement || !contentDataElement) {
        console.error('找不到标题或内容数据元素，可能模态框还未完全加载');
        console.log('所有ID为titleData的元素:', document.querySelectorAll('#titleData'));
        console.log('所有ID为contentData的元素:', document.querySelectorAll('#contentData'));
        return;
    }
    
    // 获取文本内容
    const titleText = titleDataElement.getAttribute('data-title') || '';
    const contentText = contentDataElement.getAttribute('data-content') || '';
    
    console.log('标题文本:', titleText);
    console.log('内容文本:', contentText);
    
    // 计算标题长度
    const titleLength = calculateTitleLength(titleText);
    const titleLengthElement = document.getElementById('viewTitleLength');
    const titleStatusElement = document.getElementById('viewTitleStatus');
    
    console.log('标题长度计算结果:', titleLength);
    console.log('标题长度元素:', titleLengthElement);
    console.log('标题状态元素:', titleStatusElement);
    
    if (titleLengthElement && titleStatusElement) {
        titleLengthElement.textContent = titleLength;
        
        if (titleLength > 20) {
            titleStatusElement.className = 'badge bg-danger';
            titleStatusElement.innerHTML = '<i class="bi bi-exclamation-triangle me-1"></i>标题不符合';
        } else {
            titleStatusElement.className = 'badge bg-success';
            titleStatusElement.innerHTML = '<i class="bi bi-check-circle me-1"></i>标题符合';
        }
    }
    
    // 计算内容长度
    const contentLength = calculateContentLength(contentText);
    const contentLengthElement = document.getElementById('viewContentLength');
    const contentStatusElement = document.getElementById('viewContentStatus');
    
    console.log('内容长度计算结果:', contentLength);
    console.log('内容长度元素:', contentLengthElement);
    console.log('内容状态元素:', contentStatusElement);
    
    if (contentLengthElement && contentStatusElement) {
        contentLengthElement.textContent = contentLength;
        
        if (contentLength > 1000) {
            contentStatusElement.className = 'badge bg-danger';
            contentStatusElement.innerHTML = '<i class="bi bi-exclamation-triangle me-1"></i>内容不符合';
        } else {
            contentStatusElement.className = 'badge bg-success';
            contentStatusElement.innerHTML = '<i class="bi bi-check-circle me-1"></i>内容符合';
        }
    }
    
    console.log('查看详情字符统计完成:', {
        titleLength,
        contentLength,
        titleElement: !!titleLengthElement,
        contentElement: !!contentLengthElement
    });
}

// 初始化编辑标签
function initializeEditTags() {
    console.log('开始初始化编辑标签...');

    // 清空现有标签容器
    const topicsContainer = document.getElementById('topicsContainer');
    const atUsersContainer = document.getElementById('atUsersContainer');

    if (topicsContainer) {
        topicsContainer.innerHTML = '';
    }
    if (atUsersContainer) {
        atUsersContainer.innerHTML = '';
    }

    // 初始化话题标签
    const topicsField = document.getElementById('editTopics');
    if (topicsField) {
        const topicsText = topicsField.value;
        console.log('话题数据:', topicsText);
        if (topicsText && topicsText.trim()) {
            const topics = topicsText.split('\n').filter(t => t.trim());
            console.log('解析的话题:', topics);
            topics.forEach(topic => {
                if (topic.trim()) {
                    addEditTagToContainer('topicsContainer', topic.trim(), 'topic');
                }
            });
        }
    }

    // 初始化@用户标签
    const atUsersField = document.getElementById('editAtUsers');
    if (atUsersField) {
        const atUsersText = atUsersField.value;
        console.log('@用户数据:', atUsersText);
        if (atUsersText && atUsersText.trim()) {
            const atUsers = atUsersText.split('\n').filter(u => u.trim());
            console.log('解析的@用户:', atUsers);
            atUsers.forEach(user => {
                if (user.trim()) {
                    addEditTagToContainer('atUsersContainer', user.trim(), 'at-user');
                }
            });
        }
    }

    console.log('编辑标签初始化完成');
}

// 添加标签到容器
function addEditTagToContainer(containerId, text, type) {
    const container = document.getElementById(containerId);
    if (!container) {
        console.error('找不到容器:', containerId);
        return;
    }

    // 检查是否已存在相同标签
    const existingTags = container.querySelectorAll('.tag');
    for (let existingTag of existingTags) {
        const existingText = existingTag.textContent.replace('×', '').trim();
        let compareText = text;
        if (type === 'topic' && !text.startsWith('#')) {
            compareText = '#' + text;
        } else if (type === 'at-user' && !text.startsWith('@')) {
            compareText = '@' + text;
        }
        if (existingText === compareText) {
            console.log('标签已存在:', compareText);
            return;
        }
    }

    const tag = document.createElement('span');
    tag.className = `tag ${type}`;

    let displayText = text;
    if (type === 'topic' && !text.startsWith('#')) {
        displayText = '#' + text;
    } else if (type === 'at-user' && !text.startsWith('@')) {
        displayText = '@' + text;
    }

    tag.innerHTML = `${displayText}<span class="remove">×</span>`;

    // 为删除按钮添加事件监听器
    const removeBtn = tag.querySelector('.remove');
    removeBtn.addEventListener('click', function() {
        removeEditTag(tag);
    });

    container.appendChild(tag);
    console.log('添加标签:', displayText, '到容器:', containerId);
}

// 删除标签
function removeEditTag(tagElement) {
    const container = tagElement.parentElement;
    const tagText = tagElement.textContent.replace('×', '').trim();
    console.log('删除标签:', tagText, '从容器:', container.id);

    tagElement.remove();

    // 更新对应的隐藏字段
    if (container.id === 'topicsContainer') {
        updateEditHiddenField('topicsContainer', 'editTopics');
    } else if (container.id === 'atUsersContainer') {
        updateEditHiddenField('atUsersContainer', 'editAtUsers');
    }
}

// 更新隐藏字段
function updateEditHiddenField(containerId, hiddenFieldId) {
    const container = document.getElementById(containerId);
    const hiddenField = document.getElementById(hiddenFieldId);
    if (!container || !hiddenField) return;

    const tags = container.querySelectorAll('.tag');
    const values = [];
    tags.forEach(tag => {
        let text = tag.textContent.replace('×', '').trim();
        // 移除前缀符号
        if (text.startsWith('#') || text.startsWith('@')) {
            text = text.substring(1);
        }
        values.push(text);
    });

    hiddenField.value = values.join('\n');
}

// 设置输入框事件
function setupEditInputEvents() {
    // 话题输入框
    const topicInput = document.getElementById('topicInput');
    if (topicInput) {
        topicInput.addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                e.preventDefault();
                addEditTopic();
            }
        });

        topicInput.addEventListener('blur', function() {
            const value = this.value.trim();
            if (value) {
                addEditTopic();
            }
        });
    }

    // @用户输入框
    const atUserInput = document.getElementById('atUserInput');
    if (atUserInput) {
        atUserInput.addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                e.preventDefault();
                addEditAtUser();
            }
        });

        atUserInput.addEventListener('blur', function() {
            const value = this.value.trim();
            if (value) {
                addEditAtUser();
            }
        });
    }
}

// 添加话题
function addEditTopic() {
    const input = document.getElementById('topicInput');
    const value = input.value.trim();
    if (value) {
        addEditTagToContainer('topicsContainer', value, 'topic');
        input.value = '';
        updateEditHiddenField('topicsContainer', 'editTopics');
    }
}

// 添加@用户
function addEditAtUser() {
    const input = document.getElementById('atUserInput');
    const value = input.value.trim();
    if (value) {
        addEditTagToContainer('atUsersContainer', value, 'at-user');
        input.value = '';
        updateEditHiddenField('atUsersContainer', 'editAtUsers');
    }
}

// 设置表单提交事件
function setupEditFormSubmit() {
    const form = document.getElementById('editContentForm');
    if (form) {
        console.log('设置编辑表单提交事件...');
        form.removeEventListener('submit', handleEditFormSubmit);
        form.addEventListener('submit', handleEditFormSubmit);
    }
}

// 处理表单提交
function handleEditFormSubmit(event) {
    event.preventDefault();
    const contentId = event.target.getAttribute('data-content-id');
    if (contentId) {
        const result = submitEditFormDirectly(event, contentId);
        if (result === false) {
            // 验证失败，阻止表单提交
            return false;
        }
    }
    return false;
}

// 提交编辑表单
function submitEditFormDirectly(event, contentId) {
    console.log('=== 开始提交编辑表单 ===');
    console.log('Content ID:', contentId);

    // 字符限制验证
    const titleInput = document.getElementById('editTitle');
    const contentInput = document.getElementById('editContent');
    
    if (titleInput && contentInput) {
        const titleText = titleInput.value.trim();
        const contentText = contentInput.value.trim();
        
        // 验证标题长度
        const titleLength = calculateTitleLength(titleText);
        if (titleLength > 20) {
            createSimpleToast(`标题超出限制！当前${titleLength}个字符，最多20个字符`, 'error');
            return false; // 明确返回false阻止提交
        }
        
        // 验证内容长度
        const contentLength = calculateContentLength(contentText);
        if (contentLength > 1000) {
            createSimpleToast(`内容超出限制！当前${contentLength}个字符，最多1000个字符`, 'error');
            return false; // 明确返回false阻止提交
        }
    }

    // 更新隐藏字段
    updateEditHiddenField('topicsContainer', 'editTopics');
    updateEditHiddenField('atUsersContainer', 'editAtUsers');

    const form = event.target;
    const formData = new FormData(form);

    // 打印表单数据
    console.log('=== 表单数据 ===');
    for (let [key, value] of formData.entries()) {
        console.log(key + ':', value);
    }
    console.log('=== 表单数据结束 ===');

    // 显示加载状态
    let submitBtn = form.querySelector('button[type="submit"]');
    let originalText = submitBtn ? submitBtn.innerHTML : '保存修改';
    if (submitBtn) {
        submitBtn.innerHTML = '<i class="bi bi-hourglass-split"></i> 保存中...';
        submitBtn.disabled = true;
    }

    const apiUrl = `/simple/api/content-update/${contentId}`;
    console.log('发送请求到:', apiUrl);

    fetch(apiUrl, {
        method: 'POST',
        body: formData,
        headers: {
            'X-Requested-With': 'XMLHttpRequest'
        }
    })
    .then(response => {
        console.log('收到响应，状态:', response.status);
        return response.json();
    })
    .then(data => {
        console.log('响应数据:', data);
        if (data.success) {
            // 使用toast提示替换alert
            if (typeof showToast === 'function') {
                showToast(data.message || '保存成功', 'success');
            } else if (typeof showCustomToast === 'function') {
                showCustomToast(data.message || '保存成功', 'success');
            } else {
                // 创建简单的toast提示
                createSimpleToast(data.message || '保存成功', 'success');
            }
            // 关闭模态框
            const modal = bootstrap.Modal.getInstance(document.getElementById('contentModal'));
            if (modal) {
                modal.hide();
            }
            // 刷新页面
            location.reload();
        } else {
            // 使用toast提示替换alert
            if (typeof showToast === 'function') {
                showToast(data.message || '保存失败', 'error');
            } else if (typeof showCustomToast === 'function') {
                showCustomToast(data.message || '保存失败', 'error');
            } else {
                // 创建简单的toast提示
                createSimpleToast(data.message || '保存失败', 'error');
            }
        }
    })
    .catch(error => {
        console.error('保存失败:', error);
        // 使用toast提示替换alert
        if (typeof showToast === 'function') {
            showToast('保存失败，请重试', 'error');
        } else if (typeof showCustomToast === 'function') {
            showCustomToast('保存失败，请重试', 'error');
        } else {
            // 创建简单的toast提示
            createSimpleToast('保存失败，请重试', 'error');
        }
    })
    .finally(() => {
        // 恢复按钮状态
        if (submitBtn) {
            submitBtn.innerHTML = originalText;
            submitBtn.disabled = false;
        }
    });

    return false; // 确保阻止默认表单提交
}

// 将函数设为全局，供HTML调用
window.addEditTopic = addEditTopic;
window.addEditAtUser = addEditAtUser;
window.submitEditFormDirectly = submitEditFormDirectly;

// 统一的点击事件处理器
document.addEventListener('click', function(e) {
    // 单个文案审核
    if (e.target.classList.contains('review-btn') || e.target.parentElement.classList.contains('review-btn')) {
        e.preventDefault();
        e.stopPropagation();
        const btn = e.target.classList.contains('review-btn') ? e.target : e.target.parentElement;
        const contentId = btn.dataset.contentId;
        const action = btn.dataset.action;

        if (action === 'approve') {
            reviewSingleContent(contentId, 'approved', '');
        }
        return;
    }

    // 处理删除按钮
    if (e.target.classList.contains('delete-btn') || e.target.parentElement.classList.contains('delete-btn')) {
        e.preventDefault();
        e.stopPropagation();
        const btn = e.target.classList.contains('delete-btn') ? e.target : e.target.parentElement;
        const contentId = btn.dataset.contentId;

        deleteContent(contentId);
        return;
    }

    // 批量审核
    if (e.target.classList.contains('batch-review-btn') || e.target.parentElement.classList.contains('batch-review-btn')) {
        e.preventDefault();
        e.stopPropagation();
        const btn = e.target.classList.contains('batch-review-btn') ? e.target : e.target.parentElement;
        const action = btn.dataset.action;

        const selectedCheckboxes = document.querySelectorAll('.content-checkbox:checked');
        if (selectedCheckboxes.length === 0) {
            alert('请先选择要操作的文案');
            return;
        }

        const contentIds = Array.from(selectedCheckboxes).map(cb => cb.value);

        if (action === 'approve') {
            // 防止重复操作
            if (window.batchProcessing) {
                console.log('批量操作正在进行中，防止重复调用');
                return;
            }

            const message = `确定要批量通过 ${contentIds.length} 篇文案的初审吗？`;
            if (confirm(message)) {
                batchReviewContent(contentIds, 'approved', '');
            }
        }
        return;
    }

    // 批量删除
    if (e.target.classList.contains('batch-delete-btn') || e.target.parentElement.classList.contains('batch-delete-btn')) {
        e.preventDefault();
        e.stopPropagation();
        
        // 防止重复点击
        if (window.batchDeleteProcessing) {
            console.log('批量删除正在进行中，防止重复点击');
            return;
        }
        
        const selectedCheckboxes = document.querySelectorAll('.content-checkbox:checked');
        if (selectedCheckboxes.length === 0) {
            createSimpleToast('请先选择要删除的文案', 'error');
            return;
        }

        const contentIds = Array.from(selectedCheckboxes).map(cb => cb.value);
        const message = `确定要批量删除 ${contentIds.length} 篇文案吗？删除后无法恢复。`;

        if (!confirm(message)) {
            return;
        }
        
        // 设置处理中标记
        window.batchDeleteProcessing = true;
        
        // 禁用按钮
        const btn = e.target.classList.contains('batch-delete-btn') ? e.target : e.target.parentElement;
        btn.disabled = true;
        btn.innerHTML = '<i class="bi bi-hourglass-split"></i> 删除中...';
        
        batchDeleteContent(contentIds);
        return;
    }
});

// 单个文案审核请求
function reviewSingleContent(contentId, status, reason = '') {
    const formData = new FormData();
    formData.append('status', status);
    formData.append('reason', reason);

    fetch(`/simple/contents/${contentId}/review`, {
        method: 'POST',
        body: formData,
        headers: {
            'X-CSRFToken': document.querySelector('meta[name=csrf-token]').getAttribute('content')
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            // 显示成功提示（统一使用createSimpleToast避免重复）
            createSimpleToast('通过成功', 'success');

            // 从列表中移除该条记录，而不是刷新整个页面
            removeContentFromList(contentId);
        } else {
            // 使用toast提示替换alert
            if (typeof showToast === 'function') {
                showToast('操作失败: ' + data.message, 'error');
            } else if (typeof showCustomToast === 'function') {
                showCustomToast('操作失败: ' + data.message, 'error');
            } else {
                createSimpleToast('操作失败: ' + data.message, 'error');
            }
        }
    })
    .catch(error => {
        console.error('Error:', error);
        // 使用toast提示替换alert
        if (typeof showToast === 'function') {
            showToast('操作失败，请重试', 'error');
        } else if (typeof showCustomToast === 'function') {
            showCustomToast('操作失败，请重试', 'error');
        } else {
            createSimpleToast('操作失败，请重试', 'error');
        }
    });
}

// 从列表中移除指定的文案记录
function removeContentFromList(contentId) {
    // 查找并移除对应的文案行
    const contentRow = document.querySelector(`tr[data-content-id="${contentId}"]`);
    if (contentRow) {
        // 添加淡出动画效果
        contentRow.style.transition = 'opacity 0.3s ease-out';
        contentRow.style.opacity = '0';

        // 300ms后移除元素
        setTimeout(() => {
            contentRow.remove();

            // 更新状态统计数字
            updateStatusCounts();

            // 检查是否还有文案记录
            const remainingRows = document.querySelectorAll('tbody tr[data-content-id]');
            if (remainingRows.length === 0) {
                // 如果没有记录了，显示空状态提示
                const tbody = document.querySelector('tbody');
                if (tbody) {
                    tbody.innerHTML = `
                        <tr>
                            <td colspan="9" class="text-center text-muted py-4">
                                <i class="bi bi-inbox display-1 mb-3"></i><br>
                                <h5 class="mb-2">暂无待审核的文案</h5>
                                <p class="mb-3">该客户的所有文案都已审核完成</p>
                                <p class="mb-0">
                                    <span id="redirectCountdownDynamic">3</span> 秒后自动返回客户列表，或
                                    <a href="{{ url_for('main_simple.review_content_page') }}" class="btn btn-primary btn-sm">
                                        <i class="bi bi-arrow-left"></i> 立即返回
                                    </a>
                                </p>
                            </td>
                        </tr>
                    `;

                    // 启动倒计时
                    startRedirectCountdown();
                }
            }
        }, 300);
    }
}

// 更新状态统计数字
function updateStatusCounts() {
    // 重新计算各状态的数量
    const allRows = document.querySelectorAll('tbody tr[data-content-id]');
    let draftCount = 0;
    let rejectedCount = 0;

    allRows.forEach(row => {
        const statusBadge = row.querySelector('td:nth-child(6) .badge'); // 状态列
        if (statusBadge) {
            const statusText = statusBadge.textContent.trim();
            if (statusText === '草稿') {
                draftCount++;
            } else if (statusText === '内部驳回' || statusText === '客户驳回') {
                rejectedCount++;
            }
        }
    });

    // 更新统计卡片
    const draftCountElement = document.getElementById('draft-count');
    const rejectedCountElement = document.getElementById('rejected-count');

    if (draftCountElement) draftCountElement.textContent = draftCount;
    if (rejectedCountElement) rejectedCountElement.textContent = rejectedCount;
}

// 批量审核请求
function batchReviewContent(contentIds, status, reason = '') {
    // 设置批量处理标记
    window.batchProcessing = true;

    const formData = new FormData();
    formData.append('action', 'review');
    formData.append('content_ids', contentIds.join(','));
    formData.append('status', status);
    formData.append('reason', reason);

    fetch('/simple/contents/batch-action', {
        method: 'POST',
        body: formData,
        headers: {
            'X-CSRFToken': document.querySelector('meta[name=csrf-token]').getAttribute('content')
        }
    })
    .then(response => response.json())
    .then(data => {
        // 清除批量处理标记
        window.batchProcessing = false;

        if (data.success) {
            // 统一使用createSimpleToast，优化提示文案
            createSimpleToast(`批量通过成功，共通过 ${data.count} 篇文案`, 'success');

            // 清除所有选择
            document.querySelectorAll('.content-checkbox').forEach(checkbox => {
                checkbox.checked = false;
            });
            const selectAllCheckbox = document.getElementById('selectAll');
            if (selectAllCheckbox) {
                selectAllCheckbox.checked = false;
                selectAllCheckbox.indeterminate = false;
            }
            updateSelectedCount();

            refreshReviewPage();
        } else {
            createSimpleToast('批量操作失败: ' + data.message, 'error');
        }
    })
    .catch(error => {
        // 清除批量处理标记
        window.batchProcessing = false;

        console.error('Error:', error);
        createSimpleToast('批量操作失败，请重试', 'error');
    });
}

// 刷新页面
function refreshReviewPage() {
    // 重新加载当前页面内容
    const currentUrl = new URL(window.location.href);
    const params = new URLSearchParams(currentUrl.search);

    // 构建AJAX请求URL
    let ajaxUrl = '/simple/review-content';
    if (params.toString()) {
        ajaxUrl += '?' + params.toString();
    }

    fetch(ajaxUrl, {
        headers: {
            'X-Requested-With': 'XMLHttpRequest'
        }
    })
    .then(response => response.text())
    .then(html => {
        // 更新页面内容
        document.getElementById('page-container').innerHTML = html;

        // 页面内容更新后，重新检查复选框状态
        setTimeout(() => {
            updateCheckboxStates();
        }, 100);
    })
    .catch(error => {
        console.error('Error:', error);
        createSimpleToast('刷新失败，请重试', 'error');
    });
}

// 筛选表单提交
document.getElementById('filterForm').addEventListener('submit', function(e) {
    e.preventDefault();

    const formData = new FormData(this);
    const params = new URLSearchParams(formData);

    // 更新URL
    const newUrl = '/simple/review-content?' + params.toString();

    // 发送AJAX请求
    fetch(newUrl, {
        headers: {
            'X-Requested-With': 'XMLHttpRequest'
        }
    })
    .then(response => response.text())
    .then(html => {
        // 更新页面内容
        document.getElementById('page-container').innerHTML = html;

        // 页面内容更新后，重新检查复选框状态
        setTimeout(() => {
            updateCheckboxStates();
        }, 100);

        // 更新浏览器URL（不刷新页面）
        history.pushState(null, '', newUrl);
    })
    .catch(error => {
        console.error('Error:', error);
        alert('筛选失败，请重试');
    });
});

// 客户选择变化时更新任务列表
document.getElementById('client_id').addEventListener('change', function() {
    const clientId = this.value;
    const taskSelect = document.getElementById('task_id');

    if (clientId && clientId !== '0') {
        fetch(`/simple/contents/get-tasks/${clientId}`)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                taskSelect.innerHTML = '<option value="0">全部任务</option>';
                data.tasks.forEach(task => {
                    taskSelect.innerHTML += `<option value="${task.id}">${task.name}</option>`;
                });
            }
        })
        .catch(error => {
            console.error('Error:', error);
        });
    } else {
        taskSelect.innerHTML = '<option value="0">全部任务</option>';
    }
});

// 任务选择变化时更新批次列表
document.getElementById('task_id').addEventListener('change', function() {
    const taskId = this.value;
    const batchSelect = document.getElementById('batch_id');

    if (taskId && taskId !== '0') {
        fetch(`/simple/contents/get-batches/${taskId}`)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                batchSelect.innerHTML = '<option value="0">全部批次</option>';
                data.batches.forEach(batch => {
                    batchSelect.innerHTML += `<option value="${batch.id}">${batch.name}</option>`;
                });
            }
        })
        .catch(error => {
            console.error('Error:', error);
        });
    } else {
        batchSelect.innerHTML = '<option value="0">全部批次</option>';
    }
});

// 单个文案删除
function deleteContent(contentId) {
    // 防止重复调用
    if (window.deletingContent === contentId) {
        console.log('防止重复删除:', contentId);
        return;
    }

    if (!confirm('确定要删除这篇文案吗？此操作不可恢复。')) {
        return;
    }

    // 设置删除标记
    window.deletingContent = contentId;

    const formData = new FormData();
    formData.append('auto_supplement', 'false');

    fetch(`/simple/api/contents/${contentId}/delete`, {
        method: 'POST',
        body: formData,
        headers: {
            'X-Requested-With': 'XMLHttpRequest'
        }
    })
    .then(response => {
        console.log('删除响应状态:', response.status);
        if (!response.ok) {
            throw new Error(`HTTP ${response.status}`);
        }
        return response.json();
    })
    .then(data => {
        // 清除删除标记
        window.deletingContent = null;

        if (data.success) {
            // 统一使用createSimpleToast避免重复
            createSimpleToast(data.message || '删除成功', 'success');

            // 只移除对应的行，不刷新整个页面
            removeContentFromList(contentId);
        } else {
            createSimpleToast(data.message || '删除失败', 'error');
        }
    })
    .catch(error => {
        // 清除删除标记
        window.deletingContent = null;

        console.error('删除失败:', error);
        createSimpleToast('删除失败，请重试', 'error');
    });
}

// 批量删除
function batchDeleteContent(contentIds) {
    const formData = new FormData();
    formData.append('action', 'delete');
    formData.append('content_ids', contentIds.join(','));

    fetch('/simple/contents/batch-action', {
        method: 'POST',
        body: formData,
        headers: {
            'X-CSRFToken': document.querySelector('meta[name=csrf-token]').getAttribute('content')
        }
    })
    .then(response => response.json())
    .then(data => {
        // 清除处理中标记
        window.batchDeleteProcessing = false;
        
        // 恢复按钮状态
        const batchDeleteBtn = document.querySelector('.batch-delete-btn');
        if (batchDeleteBtn) {
            batchDeleteBtn.disabled = false;
            batchDeleteBtn.innerHTML = '<i class="bi bi-trash"></i> 批量删除';
        }

        if (data.success) {
            // 使用toast提示替换alert
            if (typeof showToast === 'function') {
                showToast(`批量删除成功，共删除 ${data.count} 篇文案`, 'success');
            } else if (typeof showCustomToast === 'function') {
                showCustomToast(`批量删除成功，共删除 ${data.count} 篇文案`, 'success');
            } else {
                createSimpleToast(`批量删除成功，共删除 ${data.count} 篇文案`, 'success');
            }

            // 移除已删除的内容行，而不是刷新整个页面
            contentIds.forEach(contentId => {
                removeContentFromList(contentId);
            });
        } else {
            // 使用toast提示替换alert
            if (typeof showToast === 'function') {
                showToast('批量删除失败: ' + data.message, 'error');
            } else if (typeof showCustomToast === 'function') {
                showCustomToast('批量删除失败: ' + data.message, 'error');
            } else {
                createSimpleToast('批量删除失败: ' + data.message, 'error');
            }
        }
    })
    .catch(error => {
        // 清除处理中标记
        window.batchDeleteProcessing = false;
        
        // 恢复按钮状态
        const batchDeleteBtn = document.querySelector('.batch-delete-btn');
        if (batchDeleteBtn) {
            batchDeleteBtn.disabled = false;
            batchDeleteBtn.innerHTML = '<i class="bi bi-trash"></i> 批量删除';
        }

        console.error('批量删除失败:', error);
        // 使用toast提示替换alert
        if (typeof showToast === 'function') {
            showToast('批量删除失败，请重试', 'error');
        } else if (typeof showCustomToast === 'function') {
            showCustomToast('批量删除失败，请重试', 'error');
        } else {
            createSimpleToast('批量删除失败，请重试', 'error');
        }
    });
}

// 创建简单的toast提示函数
function createSimpleToast(message, type = 'success') {
    // 防止重复提示：检查是否已有相同消息的toast
    const existingToasts = document.querySelectorAll('.toast .toast-body');
    for (let toast of existingToasts) {
        if (toast.textContent.trim().includes(message)) {
            console.log('防止重复提示:', message);
            return; // 如果已有相同消息，直接返回
        }
    }

    // 创建toast容器（如果不存在）
    let toastContainer = document.getElementById('toast-container');
    if (!toastContainer) {
        toastContainer = document.createElement('div');
        toastContainer.id = 'toast-container';
        toastContainer.className = 'position-fixed top-0 end-0 p-3';
        toastContainer.style.zIndex = '9999';
        document.body.appendChild(toastContainer);
    }

    // 创建toast元素
    const toastId = 'toast-' + Date.now();
    const bgClass = type === 'success' ? 'bg-success' : type === 'error' ? 'bg-danger' : 'bg-info';
    const iconClass = type === 'success' ? 'fa-check-circle' : type === 'error' ? 'fa-times-circle' : 'fa-info-circle';

    const toastHtml = `
        <div class="toast align-items-center text-white ${bgClass} border-0" role="alert" aria-live="assertive" aria-atomic="true" id="${toastId}">
            <div class="d-flex">
                <div class="toast-body">
                    <i class="fas ${iconClass} me-2"></i> ${message}
                </div>
                <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast" aria-label="Close"></button>
            </div>
        </div>
    `;

    toastContainer.insertAdjacentHTML('beforeend', toastHtml);

    // 显示toast
    const toastElement = document.getElementById(toastId);
    if (typeof bootstrap !== 'undefined' && bootstrap.Toast) {
        const toast = new bootstrap.Toast(toastElement, {
            autohide: true,
            delay: 3000
        });
        toast.show();

        // 监听隐藏事件，移除DOM元素
        toastElement.addEventListener('hidden.bs.toast', function() {
            toastElement.remove();
        });
    } else {
        // 如果Bootstrap不可用，使用简单的显示/隐藏
        toastElement.style.display = 'block';
        setTimeout(() => {
            toastElement.remove();
        }, 3000);
    }
}

// 初始化
document.addEventListener('DOMContentLoaded', function() {
    updateSelectedCount();
});

// 模态框中的通过审核函数
function approveContentInModal(contentId) {
    console.log('模态框通过审核函数被调用，contentId:', contentId);

    // 检查审核按钮是否被禁用
    const approveButton = document.getElementById('approveButton');
    if (approveButton && approveButton.disabled) {
        console.log('❌ 审核按钮已禁用，阻止审核操作');
        createSimpleToast('内容长度超出限制，无法通过审核', 'error');
        return;
    }

    // 双重检查：重新计算长度并验证
    const titleDataElement = document.getElementById('titleData');
    const contentDataElement = document.getElementById('contentData');

    if (titleDataElement && contentDataElement) {
        const titleText = titleDataElement.getAttribute('data-title') || '';
        const contentText = contentDataElement.getAttribute('data-content') || '';

        const titleLength = calculateTitleLength(titleText);
        const contentLength = calculateContentLength(contentText);

        if (titleLength > 20 || contentLength > 1000) {
            console.log('❌ 长度验证失败，阻止审核操作', { titleLength, contentLength });
            createSimpleToast(`内容长度超出限制！标题: ${titleLength}/20, 内容: ${contentLength}/1000`, 'error');
            return;
        }

        console.log('✅ 长度验证通过', { titleLength, contentLength });
    }

    const formData = new FormData();
    formData.append('status', 'approved');
    formData.append('reason', '');

    fetch(`/simple/contents/${contentId}/review`, {
        method: 'POST',
        body: formData,
        headers: {
            'X-Requested-With': 'XMLHttpRequest'
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            // 显示成功提示（统一使用createSimpleToast避免重复）
            createSimpleToast('通过成功', 'success');
            // 关闭模态框
            const modal = bootstrap.Modal.getInstance(document.querySelector('.modal.show'));
            if (modal) modal.hide();
            // 从列表中移除该条记录
            removeContentFromList(contentId);
        } else {
            if (typeof showToast === 'function') {
                showToast('操作失败：' + (data.message || '未知错误'), 'error');
            } else if (typeof showCustomToast === 'function') {
                showCustomToast('操作失败：' + (data.message || '未知错误'), 'error');
            } else {
                createSimpleToast('操作失败：' + (data.message || '未知错误'), 'error');
            }
        }
    })
    .catch(error => {
        console.error('审核失败:', error);
        if (typeof showToast === 'function') {
            showToast('审核失败，请重试', 'error');
        } else if (typeof showCustomToast === 'function') {
            showCustomToast('审核失败，请重试', 'error');
        } else {
            createSimpleToast('审核失败，请重试', 'error');
        }
    });
}

// 模态框中的删除文案函数
function deleteContentInModal(contentId) {
    console.log('模态框删除函数被调用，contentId:', contentId);

    // 防止重复调用
    if (window.deletingContentModal === contentId) {
        console.log('防止重复删除（模态框）:', contentId);
        return;
    }

    if (!confirm('确定要删除这篇文案吗？此操作不可恢复。')) {
        return;
    }

    // 设置删除标记
    window.deletingContentModal = contentId;

    const formData = new FormData();
    formData.append('auto_supplement', 'false');

    fetch(`/simple/api/contents/${contentId}/delete`, {
        method: 'POST',
        body: formData,
        headers: {
            'X-Requested-With': 'XMLHttpRequest'
        }
    })
    .then(response => {
        if (!response.ok) {
            throw new Error(`HTTP ${response.status}`);
        }
        return response.json();
    })
    .then(data => {
        // 清除删除标记
        window.deletingContentModal = null;

        if (data.success) {
            // 统一使用createSimpleToast避免重复
            createSimpleToast('删除成功', 'success');
            // 关闭模态框
            const modal = bootstrap.Modal.getInstance(document.querySelector('.modal.show'));
            if (modal) modal.hide();
            // 刷新页面
            refreshReviewPage();
        } else {
            createSimpleToast('删除失败: ' + (data.message || '未知错误'), 'error');
        }
    })
    .catch(error => {
        // 清除删除标记
        window.deletingContentModal = null;

        console.error('删除失败:', error);
        createSimpleToast('删除失败，请重试', 'error');
    });
}

// 确保函数在全局作用域中可访问
window.approveContentInModal = approveContentInModal;
window.deleteContentInModal = deleteContentInModal;

// 自动跳转倒计时函数
function startRedirectCountdown() {
    let countdown = 3;
    const countdownElement = document.getElementById('redirectCountdownDynamic');

    const timer = setInterval(() => {
        countdown--;
        if (countdownElement) {
            countdownElement.textContent = countdown;
        }

        if (countdown <= 0) {
            clearInterval(timer);
            // 跳转到客户列表页面
            window.location.href = '{{ url_for("main_simple.review_content_page") }}';
        }
    }, 1000);
}

// 状态卡片点击筛选功能
function filterByStatus(status) {
    console.log('点击状态筛选:', status);

    // 更新表单中的状态选择
    const statusSelect = document.querySelector('select[name="status"]');
    if (statusSelect) {
        statusSelect.value = status;
    }

    // 提交表单进行筛选
    const form = document.querySelector('form');
    if (form) {
        form.submit();
    }
}

// 高亮当前选中的状态卡片
function highlightActiveStatusCard() {
    const currentStatus = new URLSearchParams(window.location.search).get('status') || '';
    const statusCards = document.querySelectorAll('.status-card');

    statusCards.forEach(card => {
        const cardStatus = card.getAttribute('data-status');
        if (cardStatus === currentStatus) {
            card.classList.add('border-primary', 'bg-light');
        } else {
            card.classList.remove('border-primary', 'bg-light');
        }
    });
}

// 页面加载完成后执行
document.addEventListener('DOMContentLoaded', function() {
    highlightActiveStatusCard();
});
</script>

<style>
.cursor-pointer {
    cursor: pointer;
}

.status-card {
    transition: all 0.2s ease;
    border: 2px solid transparent;
}

.status-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.1);
    border-color: #dee2e6;
}

.status-card.border-primary {
    border-color: #0d6efd !important;
    background-color: #f8f9fa !important;
}

.status-card h4 {
    font-weight: 600;
    font-size: 1.5rem;
}

.status-card small {
    font-size: 0.75rem;
    font-weight: 500;
}

/* 确保操作按钮组水平显示 */
.btn-group {
    white-space: nowrap;
    display: inline-flex !important;
}

.btn-group .btn {
    white-space: nowrap;
    flex-shrink: 0;
    font-size: 0.75rem;
}

/* 表格单元格不换行 */
td .btn-group {
    display: inline-flex !important;
}

/* 创建时间列样式 */
td:nth-child(8) {
    white-space: nowrap;
    width: 85px;
    max-width: 85px;
}

/* 操作列样式 */
td:nth-child(9) {
    white-space: nowrap;
    min-width: 320px;
}

/* 标题列样式 */
td:nth-child(3) {
    max-width: 180px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

/* 确保表格在响应式容器中也能正常显示 */
.table-responsive {
    min-height: 400px;
}

.table th, .table td {
    vertical-align: middle;
}
</style>

<script>
// 字符统计函数 - 与编辑页面保持一致
function calculateTitleLength(text) {
    let length = 0;
    for (let char of text) {
        if (char.codePointAt(0) > 127) {
            // 检查是否是中文字符
            if (/[\u4e00-\u9fff]/.test(char)) {
                length += 1; // 中文字符算1个
            } else {
                length += 1; // emoji算1个
            }
        } else {
            // 字母数字算0.5个
            length += 0.5;
        }
    }
    return Math.ceil(length); // 向上取整
}

function calculateContentLength(text) {
    let length = 0;
    for (let char of text) {
        // 检查是否是中文字符
        if (/[\u4e00-\u9fff]/.test(char)) {
            length += 2; // 中文字符算2个
        } else {
            length += 1; // 其他字符算1个
        }
    }
    return length;
}

// 页面加载完成后计算所有长度标记和按钮状态
document.addEventListener('DOMContentLoaded', function() {
    console.log('=== DOMContentLoaded 事件触发 ===');
    const lengthBadges = document.querySelectorAll('.length-badge');

    lengthBadges.forEach(function(badge) {
        const titleText = badge.getAttribute('data-title') || '';
        const contentText = badge.getAttribute('data-content') || '';

        const titleLength = calculateTitleLength(titleText);
        const contentLength = calculateContentLength(contentText);

        // 判断是否超出限制
        const isOverLimit = titleLength > 20 || contentLength > 1000;

        if (isOverLimit) {
            badge.className = 'badge bg-danger';
            badge.innerHTML = '<i class="bi bi-exclamation-triangle"></i> 超长';
            badge.title = `标题: ${titleLength}/20, 内容: ${contentLength}/1000`;
        } else {
            badge.className = 'badge bg-success';
            badge.innerHTML = '<i class="bi bi-check-circle"></i> 正常';
            badge.title = `标题: ${titleLength}/20, 内容: ${contentLength}/1000`;
        }
    });

    // 检查所有审核按钮的状态
    const reviewButtons = document.querySelectorAll('.review-btn');

    reviewButtons.forEach(function(button) {
        const titleText = button.getAttribute('data-title') || '';
        const contentText = button.getAttribute('data-content') || '';

        const titleLength = calculateTitleLength(titleText);
        const contentLength = calculateContentLength(contentText);

        // 判断是否超出限制
        const isOverLimit = titleLength > 20 || contentLength > 1000;

        if (isOverLimit) {
            // 禁用按钮
            button.disabled = true;
            button.className = 'btn btn-outline-secondary btn-sm px-2';
            button.title = '内容长度超出限制，无法通过审核';
            button.innerHTML = '<i class="bi bi-x-circle me-1"></i><span class="d-none d-lg-inline">禁用</span>';
        } else {
            // 启用按钮
            button.disabled = false;
            button.className = 'btn btn-outline-success btn-sm px-2 review-btn';
            button.title = '通过初审';
            button.innerHTML = '<i class="bi bi-check me-1"></i><span class="d-none d-lg-inline">通过</span>';
        }
    });

    // 调用复选框禁用检查函数
    updateCheckboxStates();

    // 延时再次调用，确保长度检查完成后再更新复选框状态
    setTimeout(() => {
        console.log('延时调用 updateCheckboxStates');
        updateCheckboxStates();
    }, 500);

    // 再次延时调用，确保所有DOM更新完成
    setTimeout(() => {
        console.log('最终调用 updateCheckboxStates');
        updateCheckboxStates();
    }, 1000);
});

// 添加全局函数供调试使用
window.testUpdateCheckboxStates = function() {
    console.log('手动调用 updateCheckboxStates');
    updateCheckboxStates();
};

// 添加全局函数供外部调用
window.updateCheckboxStates = updateCheckboxStates;

// 添加 MutationObserver 来监控DOM变化
const observer = new MutationObserver(function(mutations) {
    let shouldUpdate = false;
    mutations.forEach(function(mutation) {
        if (mutation.type === 'childList' || mutation.type === 'attributes') {
            // 检查是否有长度标记的变化
            const target = mutation.target;
            if (target.classList && target.classList.contains('length-badge')) {
                shouldUpdate = true;
            }
            // 检查是否有新的内容行添加
            if (mutation.addedNodes) {
                mutation.addedNodes.forEach(function(node) {
                    if (node.nodeType === 1 && node.querySelector && node.querySelector('.length-badge')) {
                        shouldUpdate = true;
                    }
                });
            }
        }
    });

    if (shouldUpdate) {
        console.log('DOM变化检测到，更新复选框状态');
        setTimeout(() => {
            updateCheckboxStates();
        }, 100);
    }
});

// 开始观察
observer.observe(document.body, {
    childList: true,
    subtree: true,
    attributes: true,
    attributeFilter: ['class', 'disabled']
});


</script>
